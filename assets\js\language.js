// ===== BILINGUAL LANGUAGE SYSTEM =====

// Translation data
const translations = {
    en: {
        // Site Title
        site_title: "BioEngage A&P",
        
        // Navigation
        nav_home: "Home",
        nav_courses: "Courses",
        nav_lectures: "Interactive Lectures",
        nav_dashboard: "Dashboard",
        nav_about: "About",
        nav_contact: "Contact",
        nav_login: "Login",
        nav_register: "Register",
        
        // Hero Section
        hero_title: "Master Anatomy & Physiology for Biomedical Engineering Excellence",
        hero_subtitle: "Bridge the gap between human biology and engineering innovation with our comprehensive interactive learning platform",
        hero_btn_lectures: "Start Interactive Lectures",
        hero_btn_courses: "Explore Courses",
        
        // Features Section
        features_title: "Why Choose BioEngage A&P LMS?",
        features_subtitle: "Advanced learning tools designed specifically for biomedical engineering students",
        feature1_title: "BME-Centric Content",
        feature1_desc: "Specialized content tailored for Biomedical Engineers and Medical Technology professionals with real-world applications.",
        feature2_title: "Interactive 3D Anatomy",
        feature2_desc: "Engage with cutting-edge 3D anatomical models, animations, and interactive visualizations.",
        feature3_title: "Real Medical Cases",
        feature3_desc: "Apply knowledge through real-world biomedical engineering challenges and clinical case studies.",
        feature4_title: "Expert-Led Learning",
        feature4_desc: "Learn from experienced professionals and researchers in the biomedical engineering field.",
        
        // Course Preview Section
        courses_preview_title: "Featured Learning Modules",
        courses_preview_subtitle: "Comprehensive anatomy and physiology courses designed for biomedical engineering applications",
        course1_title: "Cardiovascular Biomechanics",
        course1_desc: "Master the mechanical aspects of the heart and blood vessels for medical device design and cardiac engineering applications.",
        course2_title: "Neural Engineering & Electrophysiology",
        course2_desc: "Understand neural signal processing, brain-computer interfaces, and neurological device development.",
        course3_title: "Respiratory Mechanics & Ventilation",
        course3_desc: "Explore pulmonary function, gas exchange mechanics, and respiratory support device engineering.",
        duration_12h: "12 Hours",
        duration_15h: "15 Hours",
        duration_10h: "10 Hours",
        level_intermediate: "Intermediate",
        level_advanced: "Advanced",
        btn_learn_more: "Learn More",
        btn_view_all_courses: "View All Courses",
        
        // Author Section
        author_name: "Dr. Mohammed Yagoub Esmail",
        author_title: "Professor of Biomedical Engineering",
        author_affiliation: "Sudan University of Science and Technology (SUST) - BME Department",
        copyright: "© 2025 Dr. Mohammed Yagoub Esmail. All rights reserved.",
        
        // CTA Section
        cta_title: "Ready to Transform Your Understanding of Human Anatomy?",
        cta_subtitle: "Join thousands of biomedical engineering students advancing their careers with our comprehensive learning platform",
        cta_btn_register: "Start Learning Today",
        cta_btn_demo: "Try Interactive Demo",
        
        // Footer
        footer_platform: "Platform",
        footer_support: "Support",
        footer_legal: "Legal",
        footer_connect: "Connect",
        footer_help: "Help Center",
        footer_faq: "FAQ",
        footer_privacy: "Privacy Policy",
        footer_terms: "Terms of Service",
        footer_copyright: "© 2025 BioEngage A&P LMS. Developed by Dr. Mohammed Yagoub Esmail, SUST-BME. All rights reserved.",
        
        // Loading
        loading_text: "Loading BioEngage A&P LMS...",

        // Lectures Page
        lecture_title: "Interactive Human Body Anatomy Lectures",
        lecture_subtitle: "Comprehensive anatomy presentation system for biomedical engineering students",
        institution: "SUST - BME Department",
        btn_start_lecture: "Start Lecture",
        btn_anatomy_viewer: "3D Anatomy Viewer",
        systems_title: "Body Systems Covered",
        system_cardiovascular: "Cardiovascular System",
        system_cardiovascular_desc: "Heart, blood vessels, and circulation mechanics",
        system_respiratory: "Respiratory System",
        system_respiratory_desc: "Lungs, airways, and gas exchange mechanisms",
        system_nervous: "Nervous System",
        system_nervous_desc: "Brain, spinal cord, and neural networks",
        system_musculoskeletal: "Musculoskeletal System",
        system_musculoskeletal_desc: "Bones, muscles, and movement mechanics",
        duration_45min: "45 min",
        duration_40min: "40 min",
        duration_60min: "60 min",
        duration_50min: "50 min",
        slides_count: "25 slides",
        slides_count_20: "20 slides",
        slides_count_35: "35 slides",
        slides_count_30: "30 slides",
        anatomy_viewer_title: "Interactive 3D Anatomy Viewer",
        select_system: "Select Body System",
        viewer_controls: "Controls",
        opacity: "Opacity",
        rotation_speed: "Rotation Speed",
        reset_view: "Reset View",
        show_info: "Show Info",
        model_loading: "Loading 3D Model...",
        weight: "Weight",
        size: "Size",
        function: "Function",
        pumps_blood: "Pumps Blood",

        // Common
        loading: "Loading...",
        error: "Error",
        success: "Success",
        warning: "Warning",
        info: "Information"
    },
    
    ar: {
        // Site Title
        site_title: "منصة التشريح ووظائف الأعضاء",
        
        // Navigation
        nav_home: "الرئيسية",
        nav_courses: "الدورات",
        nav_lectures: "المحاضرات التفاعلية",
        nav_dashboard: "لوحة التحكم",
        nav_about: "حول",
        nav_contact: "اتصل بنا",
        nav_login: "تسجيل الدخول",
        nav_register: "إنشاء حساب",
        
        // Hero Section
        hero_title: "إتقان التشريح ووظائف الأعضاء للتميز في الهندسة الطبية الحيوية",
        hero_subtitle: "اربط الفجوة بين علم الأحياء البشري والابتكار الهندسي مع منصتنا التعليمية التفاعلية الشاملة",
        hero_btn_lectures: "ابدأ المحاضرات التفاعلية",
        hero_btn_courses: "استكشف الدورات",
        
        // Features Section
        features_title: "لماذا تختار منصة التشريح ووظائف الأعضاء؟",
        features_subtitle: "أدوات تعليمية متقدمة مصممة خصيصاً لطلاب الهندسة الطبية الحيوية",
        feature1_title: "محتوى متخصص للهندسة الطبية",
        feature1_desc: "محتوى متخصص مصمم للمهندسين الطبيين الحيويين ومتخصصي التكنولوجيا الطبية مع تطبيقات عملية.",
        feature2_title: "تشريح ثلاثي الأبعاد تفاعلي",
        feature2_desc: "تفاعل مع نماذج تشريحية ثلاثية الأبعاد متطورة ورسوم متحركة وتصورات تفاعلية.",
        feature3_title: "حالات طبية حقيقية",
        feature3_desc: "طبق المعرفة من خلال تحديات الهندسة الطبية الحيوية الحقيقية ودراسات الحالات السريرية.",
        feature4_title: "تعلم بقيادة الخبراء",
        feature4_desc: "تعلم من المتخصصين ذوي الخبرة والباحثين في مجال الهندسة الطبية الحيوية.",
        
        // Course Preview Section
        courses_preview_title: "وحدات التعلم المميزة",
        courses_preview_subtitle: "دورات شاملة في التشريح ووظائف الأعضاء مصممة لتطبيقات الهندسة الطبية الحيوية",
        course1_title: "الميكانيكا الحيوية للقلب والأوعية الدموية",
        course1_desc: "إتقان الجوانب الميكانيكية للقلب والأوعية الدموية لتصميم الأجهزة الطبية وتطبيقات هندسة القلب.",
        course2_title: "الهندسة العصبية والفيزيولوجيا الكهربائية",
        course2_desc: "فهم معالجة الإشارات العصبية وواجهات الدماغ والحاسوب وتطوير الأجهزة العصبية.",
        course3_title: "ميكانيكا التنفس والتهوية",
        course3_desc: "استكشف وظائف الرئة وميكانيكا تبادل الغازات وهندسة أجهزة دعم التنفس.",
        duration_12h: "12 ساعة",
        duration_15h: "15 ساعة",
        duration_10h: "10 ساعات",
        level_intermediate: "متوسط",
        level_advanced: "متقدم",
        btn_learn_more: "تعلم المزيد",
        btn_view_all_courses: "عرض جميع الدورات",
        
        // Author Section
        author_name: "د. محمد يعقوب إسماعيل",
        author_title: "أستاذ الهندسة الطبية الحيوية",
        author_affiliation: "جامعة السودان للعلوم والتكنولوجيا (SUST) - قسم الهندسة الطبية الحيوية",
        copyright: "© 2025 د. محمد يعقوب إسماعيل. جميع الحقوق محفوظة.",
        
        // CTA Section
        cta_title: "هل أنت مستعد لتحويل فهمك لتشريح الإنسان؟",
        cta_subtitle: "انضم إلى آلاف طلاب الهندسة الطبية الحيوية الذين يطورون مسيرتهم المهنية مع منصتنا التعليمية الشاملة",
        cta_btn_register: "ابدأ التعلم اليوم",
        cta_btn_demo: "جرب العرض التفاعلي",
        
        // Footer
        footer_platform: "المنصة",
        footer_support: "الدعم",
        footer_legal: "قانوني",
        footer_connect: "تواصل",
        footer_help: "مركز المساعدة",
        footer_faq: "الأسئلة الشائعة",
        footer_privacy: "سياسة الخصوصية",
        footer_terms: "شروط الخدمة",
        footer_copyright: "© 2025 منصة التشريح ووظائف الأعضاء. تطوير د. محمد يعقوب إسماعيل، SUST-BME. جميع الحقوق محفوظة.",
        
        // Loading
        loading_text: "جاري تحميل منصة التشريح ووظائف الأعضاء...",

        // Lectures Page
        lecture_title: "محاضرات تشريح جسم الإنسان التفاعلية",
        lecture_subtitle: "نظام عرض تشريحي شامل لطلاب الهندسة الطبية الحيوية",
        institution: "SUST - قسم الهندسة الطبية الحيوية",
        btn_start_lecture: "ابدأ المحاضرة",
        btn_anatomy_viewer: "عارض التشريح ثلاثي الأبعاد",
        systems_title: "أجهزة الجسم المغطاة",
        system_cardiovascular: "الجهاز القلبي الوعائي",
        system_cardiovascular_desc: "القلب والأوعية الدموية وميكانيكا الدورة الدموية",
        system_respiratory: "الجهاز التنفسي",
        system_respiratory_desc: "الرئتان والمجاري التنفسية وآليات تبادل الغازات",
        system_nervous: "الجهاز العصبي",
        system_nervous_desc: "الدماغ والحبل الشوكي والشبكات العصبية",
        system_musculoskeletal: "الجهاز العضلي الهيكلي",
        system_musculoskeletal_desc: "العظام والعضلات وميكانيكا الحركة",
        duration_45min: "45 دقيقة",
        duration_40min: "40 دقيقة",
        duration_60min: "60 دقيقة",
        duration_50min: "50 دقيقة",
        slides_count: "25 شريحة",
        slides_count_20: "20 شريحة",
        slides_count_35: "35 شريحة",
        slides_count_30: "30 شريحة",
        anatomy_viewer_title: "عارض التشريح ثلاثي الأبعاد التفاعلي",
        select_system: "اختر جهاز الجسم",
        viewer_controls: "عناصر التحكم",
        opacity: "الشفافية",
        rotation_speed: "سرعة الدوران",
        reset_view: "إعادة تعيين العرض",
        show_info: "إظهار المعلومات",
        model_loading: "جاري تحميل النموذج ثلاثي الأبعاد...",
        weight: "الوزن",
        size: "الحجم",
        function: "الوظيفة",
        pumps_blood: "ضخ الدم",

        // Common
        loading: "جاري التحميل...",
        error: "خطأ",
        success: "نجح",
        warning: "تحذير",
        info: "معلومات"
    }
};

// Current language
let currentLang = 'en';

// Initialize language system
function initLanguageSystem() {
    // Get saved language or default to English
    currentLang = localStorage.getItem('preferred-language') || 'en';
    
    // Set initial language
    updateTranslations(currentLang);
    
    // Add event listeners to language buttons
    document.querySelectorAll('.lang-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const lang = btn.dataset.lang;
            switchLanguage(lang);
        });
    });
}

// Switch language function
function switchLanguage(lang) {
    if (lang === currentLang) return;
    
    currentLang = lang;
    
    // Update UI
    updateLanguageButtons(lang);
    updateBodyDirection(lang);
    updateTranslations(lang);
    
    // Save preference
    localStorage.setItem('preferred-language', lang);
    
    // Trigger custom event
    document.dispatchEvent(new CustomEvent('languageChanged', {
        detail: { language: lang }
    }));
}

// Update language buttons
function updateLanguageButtons(lang) {
    document.querySelectorAll('.lang-btn').forEach(btn => {
        btn.classList.toggle('active', btn.dataset.lang === lang);
    });
}

// Update body direction and classes
function updateBodyDirection(lang) {
    const body = document.body;
    const html = document.documentElement;
    
    if (lang === 'ar') {
        body.classList.remove('english-mode');
        body.classList.add('arabic-mode');
        html.setAttribute('lang', 'ar');
        html.setAttribute('dir', 'rtl');
    } else {
        body.classList.remove('arabic-mode');
        body.classList.add('english-mode');
        html.setAttribute('lang', 'en');
        html.setAttribute('dir', 'ltr');
    }
}

// Update all translations on the page
function updateTranslations(lang) {
    const elements = document.querySelectorAll('[data-translate]');
    
    elements.forEach(element => {
        const key = element.getAttribute('data-translate');
        const translation = getTranslation(key, lang);
        
        if (translation) {
            // Handle different element types
            if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                if (element.type === 'submit' || element.type === 'button') {
                    element.value = translation;
                } else {
                    element.placeholder = translation;
                }
            } else {
                element.textContent = translation;
            }
        }
    });
    
    // Update document title
    const titleKey = document.querySelector('[data-translate="site_title"]');
    if (titleKey) {
        document.title = getTranslation('site_title', lang) + ' - Biomedical Engineering Learning Platform';
    }
}

// Get translation for a specific key
function getTranslation(key, lang = currentLang) {
    return translations[lang] && translations[lang][key] ? translations[lang][key] : translations['en'][key] || key;
}

// Add translation dynamically
function addTranslation(key, enText, arText) {
    translations.en[key] = enText;
    translations.ar[key] = arText;
}

// Get current language
function getCurrentLanguage() {
    return currentLang;
}

// Check if current language is RTL
function isRTL() {
    return currentLang === 'ar';
}

// Format numbers based on language
function formatNumber(number, lang = currentLang) {
    if (lang === 'ar') {
        // Convert to Arabic-Indic numerals
        const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        return number.toString().replace(/\d/g, (digit) => arabicNumerals[parseInt(digit)]);
    }
    return number.toString();
}

// Format date based on language
function formatDate(date, lang = currentLang) {
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    };
    
    if (lang === 'ar') {
        return new Intl.DateTimeFormat('ar-SA', options).format(date);
    }
    return new Intl.DateTimeFormat('en-US', options).format(date);
}

// Translate dynamic content
function translateDynamicContent(content, lang = currentLang) {
    // This function can be used to translate content that's loaded dynamically
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = content;
    
    const elements = tempDiv.querySelectorAll('[data-translate]');
    elements.forEach(element => {
        const key = element.getAttribute('data-translate');
        const translation = getTranslation(key, lang);
        if (translation) {
            element.textContent = translation;
        }
    });
    
    return tempDiv.innerHTML;
}

// Language-specific CSS classes
function updateLanguageSpecificStyles() {
    const body = document.body;
    
    // Remove all language-specific classes
    body.classList.remove('lang-en', 'lang-ar');
    
    // Add current language class
    body.classList.add(`lang-${currentLang}`);
}

// Handle form validation messages in different languages
function getValidationMessage(type, lang = currentLang) {
    const validationMessages = {
        en: {
            required: 'This field is required',
            email: 'Please enter a valid email address',
            phone: 'Please enter a valid phone number',
            password: 'Password must be at least 8 characters long',
            confirm_password: 'Passwords do not match',
            terms: 'Please accept the terms and conditions'
        },
        ar: {
            required: 'هذا الحقل مطلوب',
            email: 'يرجى إدخال عنوان بريد إلكتروني صحيح',
            phone: 'يرجى إدخال رقم هاتف صحيح',
            password: 'يجب أن تكون كلمة المرور 8 أحرف على الأقل',
            confirm_password: 'كلمات المرور غير متطابقة',
            terms: 'يرجى قبول الشروط والأحكام'
        }
    };
    
    return validationMessages[lang][type] || validationMessages['en'][type];
}

// Export functions for global use
window.updateTranslations = updateTranslations;
window.switchLanguage = switchLanguage;
window.getTranslation = getTranslation;
window.getCurrentLanguage = getCurrentLanguage;
window.isRTL = isRTL;
window.formatNumber = formatNumber;
window.formatDate = formatDate;
window.translateDynamicContent = translateDynamicContent;
window.getValidationMessage = getValidationMessage;
window.addTranslation = addTranslation;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initLanguageSystem);

// Listen for language change events
document.addEventListener('languageChanged', (e) => {
    console.log('Language changed to:', e.detail.language);
    updateLanguageSpecificStyles();
});
