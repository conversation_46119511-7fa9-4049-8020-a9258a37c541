/* ===== KEYFRAME ANIMATIONS ===== */

/* Fade In Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Scale Animations */
@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes scaleUp {
    from {
        transform: scale(1);
    }
    to {
        transform: scale(1.05);
    }
}

/* Rotation Animations */
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes rotateY {
    from {
        transform: rotateY(0deg);
    }
    to {
        transform: rotateY(360deg);
    }
}

/* Floating Animations */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes floatSlow {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Pulse Animations */
@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}

@keyframes heartbeat {
    0%, 100% {
        transform: scale(1);
    }
    25% {
        transform: scale(1.1);
    }
    50% {
        transform: scale(1);
    }
    75% {
        transform: scale(1.05);
    }
}

/* Breathing Animation */
@keyframes breathe {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.08);
        opacity: 0.9;
    }
}

/* Bounce Animations */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Slide Animations */
@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Flip Animations */
@keyframes flipInX {
    from {
        transform: perspective(400px) rotateX(90deg);
        opacity: 0;
    }
    40% {
        transform: perspective(400px) rotateX(-20deg);
    }
    60% {
        transform: perspective(400px) rotateX(10deg);
        opacity: 1;
    }
    80% {
        transform: perspective(400px) rotateX(-5deg);
    }
    to {
        transform: perspective(400px) rotateX(0deg);
        opacity: 1;
    }
}

@keyframes flipInY {
    from {
        transform: perspective(400px) rotateY(90deg);
        opacity: 0;
    }
    40% {
        transform: perspective(400px) rotateY(-20deg);
    }
    60% {
        transform: perspective(400px) rotateY(10deg);
        opacity: 1;
    }
    80% {
        transform: perspective(400px) rotateY(-5deg);
    }
    to {
        transform: perspective(400px) rotateY(0deg);
        opacity: 1;
    }
}

/* Shake Animation */
@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-10px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(10px);
    }
}

/* Wobble Animation */
@keyframes wobble {
    0% {
        transform: translateX(0%);
    }
    15% {
        transform: translateX(-25%) rotate(-5deg);
    }
    30% {
        transform: translateX(20%) rotate(3deg);
    }
    45% {
        transform: translateX(-15%) rotate(-3deg);
    }
    60% {
        transform: translateX(10%) rotate(2deg);
    }
    75% {
        transform: translateX(-5%) rotate(-1deg);
    }
    100% {
        transform: translateX(0%);
    }
}

/* Spinner Animation */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Glow Animation */
@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(255, 153, 0, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(255, 153, 0, 0.8), 0 0 30px rgba(255, 153, 0, 0.6);
    }
}

/* Typing Animation */
@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink {
    0%, 50% {
        border-color: transparent;
    }
    51%, 100% {
        border-color: var(--primary-color);
    }
}

/* ===== ANIMATION UTILITY CLASSES ===== */

/* Basic Animations */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.fade-in-up {
    animation: fadeInUp 0.8s ease-out;
}

.fade-in-down {
    animation: fadeInDown 0.8s ease-out;
}

.fade-in-left {
    animation: fadeInLeft 0.8s ease-out;
}

.fade-in-right {
    animation: fadeInRight 0.8s ease-out;
}

.scale-in {
    animation: scaleIn 0.6s ease-out;
}

.bounce-in {
    animation: bounceIn 1s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.8s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.8s ease-out;
}

.slide-in-up {
    animation: slideInUp 0.8s ease-out;
}

.slide-in-down {
    animation: slideInDown 0.8s ease-out;
}

.flip-in-x {
    animation: flipInX 1s ease-out;
}

.flip-in-y {
    animation: flipInY 1s ease-out;
}

/* Continuous Animations */
.float-animation {
    animation: float 3s ease-in-out infinite;
}

.pulse-animation {
    animation: pulse 2s ease-in-out infinite;
}

.breathe-animation {
    animation: breathe 4s ease-in-out infinite;
}

.rotate-animation {
    animation: rotate 10s linear infinite;
}

.glow-animation {
    animation: glow 2s ease-in-out infinite;
}

/* Hover Animations */
.hover-scale:hover {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    transition: transform 0.3s ease;
}

.hover-rotate:hover {
    transform: rotate(5deg);
    transition: transform 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(255, 153, 0, 0.6);
    transition: box-shadow 0.3s ease;
}

/* Delayed Animations */
.delay-100 {
    animation-delay: 0.1s;
}

.delay-200 {
    animation-delay: 0.2s;
}

.delay-300 {
    animation-delay: 0.3s;
}

.delay-400 {
    animation-delay: 0.4s;
}

.delay-500 {
    animation-delay: 0.5s;
}

.delay-600 {
    animation-delay: 0.6s;
}

.delay-700 {
    animation-delay: 0.7s;
}

.delay-800 {
    animation-delay: 0.8s;
}

.delay-900 {
    animation-delay: 0.9s;
}

.delay-1000 {
    animation-delay: 1s;
}

/* Animation Duration Classes */
.duration-fast {
    animation-duration: 0.3s;
}

.duration-normal {
    animation-duration: 0.6s;
}

.duration-slow {
    animation-duration: 1s;
}

.duration-slower {
    animation-duration: 2s;
}

/* Animation Fill Mode */
.fill-both {
    animation-fill-mode: both;
}

.fill-forwards {
    animation-fill-mode: forwards;
}

.fill-backwards {
    animation-fill-mode: backwards;
}

/* Pause Animation */
.animation-paused {
    animation-play-state: paused;
}

/* ===== SPECIAL MEDICAL ANIMATIONS ===== */

/* Heart Beat Animation for Medical Icons */
.heartbeat {
    animation: heartbeat 1.5s ease-in-out infinite;
}

/* Brain Wave Animation */
@keyframes brainwave {
    0%, 100% {
        opacity: 0.6;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.02);
    }
}

.brainwave {
    animation: brainwave 2s ease-in-out infinite;
}

/* Blood Flow Animation */
@keyframes bloodflow {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.bloodflow {
    background: linear-gradient(-45deg, #ff6b6b, #ee5a52, #ff6b6b, #ee5a52);
    background-size: 400% 400%;
    animation: bloodflow 3s ease infinite;
}

/* Respiratory Animation */
@keyframes respiratory {
    0%, 100% {
        transform: scale(1);
    }
    25% {
        transform: scale(1.1);
    }
    50% {
        transform: scale(1);
    }
    75% {
        transform: scale(1.05);
    }
}

.respiratory {
    animation: respiratory 4s ease-in-out infinite;
}

/* Neural Activity Animation */
@keyframes neural {
    0%, 100% {
        opacity: 0.3;
        transform: scale(0.95);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
}

.neural {
    animation: neural 1.8s ease-in-out infinite;
}

/* Muscle Contraction Animation */
@keyframes muscle {
    0%, 100% {
        transform: scaleX(1);
    }
    50% {
        transform: scaleX(1.1);
    }
}

.muscle {
    animation: muscle 2s ease-in-out infinite;
}

/* ===== INTERSECTION OBSERVER ANIMATIONS ===== */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

/* ===== LOADING ANIMATIONS ===== */
.loading-dots {
    display: inline-block;
}

.loading-dots::after {
    content: '';
    animation: dots 2s infinite;
}

@keyframes dots {
    0%, 20% {
        content: '';
    }
    40% {
        content: '.';
    }
    60% {
        content: '..';
    }
    80%, 100% {
        content: '...';
    }
}

/* ===== RESPONSIVE ANIMATION CONTROLS ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

@media (max-width: 768px) {
    .mobile-no-animation {
        animation: none !important;
        transform: none !important;
    }
}
