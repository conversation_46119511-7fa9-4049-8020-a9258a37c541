/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-color: #0f4c75;
    --secondary-color: #ff9900;
    --accent-color: #3282b8;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    
    /* Neutral Colors */
    --white: #ffffff;
    --light-gray: #f8f9fa;
    --gray: #6c757d;
    --dark-gray: #343a40;
    --black: #000000;
    
    /* Background Colors */
    --bg-primary: #f4f4f9;
    --bg-secondary: #ffffff;
    --bg-dark: #1a1a1a;
    
    /* Text Colors */
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-light: #999999;
    --text-white: #ffffff;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;
    
    /* Typography */
    --font-primary: 'Inter', sans-serif;
    --font-arabic: 'Tajawal', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-full: 9999px;
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Base Typography */
body {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    overflow-x: hidden;
}

/* Arabic Mode Styles */
body.arabic-mode {
    font-family: var(--font-arabic);
    direction: rtl;
    text-align: right;
}

body.arabic-mode .navbar {
    flex-direction: row-reverse;
}

body.arabic-mode .nav-links {
    flex-direction: row-reverse;
}

body.arabic-mode .hero-content {
    flex-direction: row-reverse;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* ===== LANGUAGE SWITCHER ===== */
.language-switcher {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    gap: var(--spacing-xs);
    background: var(--white);
    border-radius: var(--radius-full);
    padding: var(--spacing-xs);
    box-shadow: var(--shadow-lg);
}

.lang-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    background: transparent;
    border-radius: var(--radius-full);
    cursor: pointer;
    transition: var(--transition-normal);
    font-weight: 500;
    color: var(--text-secondary);
}

.lang-btn.active {
    background: var(--primary-color);
    color: var(--white);
}

.flag-icon {
    width: 20px;
    height: 15px;
    object-fit: cover;
    border-radius: var(--radius-sm);
}

/* ===== HEADER ===== */
.main-header {
    background: var(--white);
    box-shadow: var(--shadow-md);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    transition: var(--transition-normal);
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) 0;
}

.logo a {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    text-decoration: none;
    color: var(--primary-color);
    font-size: var(--font-size-xl);
    font-weight: 700;
}

.logo i {
    font-size: var(--font-size-2xl);
    color: var(--secondary-color);
}

.nav-links {
    display: flex;
    list-style: none;
    align-items: center;
    gap: var(--spacing-lg);
}

.nav-links a {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    transition: var(--transition-normal);
    position: relative;
}

.nav-links a:hover {
    color: var(--primary-color);
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--secondary-color);
    transition: var(--transition-normal);
}

.nav-links a:hover::after {
    width: 100%;
}

.auth-buttons {
    display: flex;
    gap: var(--spacing-sm);
}

.btn-login {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: 2px solid var(--primary-color);
    border-radius: var(--radius-lg);
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-normal);
}

.btn-login:hover {
    background: var(--primary-color);
    color: var(--white);
}

.btn-register {
    padding: var(--spacing-sm) var(--spacing-lg);
    background: var(--secondary-color);
    border: 2px solid var(--secondary-color);
    border-radius: var(--radius-lg);
    color: var(--white);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-normal);
}

.btn-register:hover {
    background: transparent;
    color: var(--secondary-color);
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: var(--primary-color);
    transition: var(--transition-normal);
}

/* ===== HERO SECTION ===== */
.hero-section {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: var(--white);
    overflow: hidden;
    margin-top: 80px;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('../images/hero-bg.jpg') center/cover;
    opacity: 0.1;
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('../images/particles.png') repeat;
    animation: float 20s infinite linear;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xxl);
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-lg);
    animation: fadeInUp 1s ease-out;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    line-height: 1.6;
    margin-bottom: var(--spacing-xl);
    opacity: 0.9;
    animation: fadeInUp 1s ease-out 0.2s both;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-lg);
    animation: fadeInUp 1s ease-out 0.4s both;
}

.btn-primary, .btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg) var(--spacing-xl);
    border-radius: var(--radius-lg);
    text-decoration: none;
    font-weight: 600;
    font-size: var(--font-size-lg);
    transition: var(--transition-normal);
    border: 2px solid transparent;
}

.btn-primary {
    background: var(--secondary-color);
    color: var(--white);
    border-color: var(--secondary-color);
}

.btn-primary:hover {
    background: transparent;
    color: var(--secondary-color);
    border-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: transparent;
    color: var(--white);
    border-color: var(--white);
}

.btn-secondary:hover {
    background: var(--white);
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Hero Visual */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    animation: fadeInRight 1s ease-out 0.6s both;
}

.anatomy-preview {
    position: relative;
    width: 300px;
    height: 300px;
}

.rotating-model {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    backdrop-filter: blur(10px);
    animation: rotate 20s infinite linear;
}

.rotating-model i {
    font-size: 120px;
    color: var(--secondary-color);
}

.floating-icons {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.floating-icons i {
    position: absolute;
    font-size: 40px;
    color: var(--white);
}

.floating-icons .fa-heart {
    top: 20%;
    right: 10%;
    color: #ff6b6b;
}

.floating-icons .fa-brain {
    top: 60%;
    left: 5%;
    color: #4ecdc4;
}

.floating-icons .fa-lungs {
    bottom: 20%;
    right: 20%;
    color: #45b7d1;
}

/* ===== SECTIONS ===== */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-xxl);
}

.section-header h2 {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.section-header p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== FEATURES SECTION ===== */
.features-section {
    padding: var(--spacing-xxl) 0;
    background: var(--white);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
}

.feature-card {
    text-align: center;
    padding: var(--spacing-xl);
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    transition: var(--transition-normal);
    border: 1px solid transparent;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
    border-color: var(--secondary-color);
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg);
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.feature-icon i {
    font-size: var(--font-size-2xl);
    color: var(--white);
}

.feature-card h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* ===== COURSE PREVIEW SECTION ===== */
.course-preview-section {
    padding: var(--spacing-xxl) 0;
    background: var(--bg-primary);
}

.course-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.course-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.course-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.course-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.course-card:hover .course-image img {
    transform: scale(1.1);
}

.course-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(15, 76, 117, 0.8), rgba(50, 130, 184, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-normal);
}

.course-card:hover .course-overlay {
    opacity: 1;
}

.course-overlay i {
    font-size: var(--font-size-4xl);
    color: var(--white);
}

.course-content {
    padding: var(--spacing-xl);
}

.course-content h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.course-content p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.course-meta {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.course-meta span {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-light);
}

.btn-course {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    background: var(--secondary-color);
    color: var(--white);
    text-decoration: none;
    border-radius: var(--radius-lg);
    font-weight: 500;
    transition: var(--transition-normal);
}

.btn-course:hover {
    background: var(--primary-color);
    transform: translateX(5px);
}

.view-all-courses {
    text-align: center;
}

/* ===== AUTHOR SECTION ===== */
.author-section {
    padding: var(--spacing-xxl) 0;
    background: var(--white);
}

.author-card {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: var(--spacing-xl);
    align-items: center;
    max-width: 800px;
    margin: 0 auto;
    padding: var(--spacing-xl);
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
}

.author-image {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid var(--secondary-color);
}

.author-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.author-info h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.author-title {
    font-size: var(--font-size-lg);
    color: var(--secondary-color);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

.author-affiliation {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

.author-contact p {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}

.copyright {
    margin-top: var(--spacing-lg);
    font-size: var(--font-size-sm);
    color: var(--text-light);
    font-style: italic;
}

/* ===== CTA SECTION ===== */
.cta-section {
    padding: var(--spacing-xxl) 0;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white);
    text-align: center;
}

.cta-content h2 {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
}

.cta-content p {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xl);
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

/* ===== FOOTER ===== */
.main-footer {
    background: var(--dark-gray);
    color: var(--white);
    padding: var(--spacing-xxl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.footer-section h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    color: var(--secondary-color);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: var(--spacing-sm);
}

.footer-section ul li a {
    color: var(--text-light);
    text-decoration: none;
    transition: var(--transition-normal);
}

.footer-section ul li a:hover {
    color: var(--white);
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
}

.social-link {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    transition: var(--transition-normal);
}

.social-link:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid var(--gray);
    padding-top: var(--spacing-lg);
    text-align: center;
    color: var(--text-light);
}

/* ===== LOADING SCREEN ===== */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease-out;
}

.loading-screen.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-content {
    text-align: center;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--bg-primary);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-lg);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-sm);
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-xl);
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .course-grid {
        grid-template-columns: 1fr;
    }
    
    .author-card {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .nav-links {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .language-switcher {
        top: 10px;
        right: 10px;
    }
}
