<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="author" content="<PERSON><PERSON>, SUST - BME">
    <meta name="description" content="Interactive Anatomy Lectures - BioEngage A&P LMS">
    <title>Interactive Anatomy Lectures - BioEngage A&P LMS</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/animations.css">
    <link rel="stylesheet" href="assets/css/lectures.css">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- JavaScript Files -->
    <script src="assets/js/main.js" defer></script>
    <script src="assets/js/language.js" defer></script>
    <script src="assets/js/lectures.js" defer></script>
</head>
<body class="english-mode lectures-page">
    <!-- Language Switcher -->
    <div class="language-switcher">
        <button id="lang-en" class="lang-btn active" data-lang="en">
            <img src="assets/images/flags/us.png" alt="English" class="flag-icon">
            EN
        </button>
        <button id="lang-ar" class="lang-btn" data-lang="ar">
            <img src="assets/images/flags/sa.png" alt="العربية" class="flag-icon">
            AR
        </button>
    </div>

    <!-- Header Section -->
    <header class="main-header">
        <div class="container">
            <nav class="navbar">
                <div class="logo">
                    <a href="index.html">
                        <i class="fas fa-heartbeat"></i>
                        <span data-translate="site_title">BioEngage A&P</span>
                    </a>
                </div>
                <ul class="nav-links">
                    <li><a href="index.html" data-translate="nav_home">Home</a></li>
                    <li><a href="courses.html" data-translate="nav_courses">Courses</a></li>
                    <li><a href="lectures.html" class="active" data-translate="nav_lectures">Interactive Lectures</a></li>
                    <li><a href="dashboard.html" data-translate="nav_dashboard">Dashboard</a></li>
                    <li><a href="about.html" data-translate="nav_about">About</a></li>
                    <li><a href="contact.html" data-translate="nav_contact">Contact</a></li>
                    <li class="auth-buttons">
                        <a href="login.html" class="btn-login" data-translate="nav_login">Login</a>
                        <a href="register.html" class="btn-register" data-translate="nav_register">Register</a>
                    </li>
                </ul>
                <div class="mobile-menu-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="lectures-main" id="main-content">
        <!-- Lecture Header -->
        <section class="lecture-header">
            <div class="container">
                <div class="lecture-header-content">
                    <div class="lecture-info">
                        <h1 data-translate="lecture_title">Interactive Human Body Anatomy Lectures</h1>
                        <p data-translate="lecture_subtitle">Comprehensive anatomy presentation system for biomedical engineering students</p>
                        <div class="lecture-meta">
                            <span class="author-info">
                                <i class="fas fa-user-md"></i>
                                <span data-translate="author_name">Dr. Mohammed Yagoub Esmail</span>
                            </span>
                            <span class="institution">
                                <i class="fas fa-university"></i>
                                <span data-translate="institution">SUST - BME Department</span>
                            </span>
                            <span class="year">
                                <i class="fas fa-calendar"></i>
                                <span>2025</span>
                            </span>
                        </div>
                    </div>
                    <div class="lecture-controls">
                        <button class="btn-start-lecture" id="startLectureBtn" data-translate="btn_start_lecture">
                            <i class="fas fa-play"></i>
                            Start Lecture
                        </button>
                        <button class="btn-anatomy-viewer" id="anatomyViewerBtn" data-translate="btn_anatomy_viewer">
                            <i class="fas fa-cube"></i>
                            3D Anatomy Viewer
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Lecture Systems Overview -->
        <section class="lecture-systems">
            <div class="container">
                <h2 data-translate="systems_title">Body Systems Covered</h2>
                <div class="systems-grid">
                    <div class="system-card" data-system="cardiovascular">
                        <div class="system-icon">
                            <i class="fas fa-heart"></i>
                        </div>
                        <h3 data-translate="system_cardiovascular">Cardiovascular System</h3>
                        <p data-translate="system_cardiovascular_desc">Heart, blood vessels, and circulation mechanics</p>
                        <div class="system-stats">
                            <span><i class="fas fa-clock"></i> <span data-translate="duration_45min">45 min</span></span>
                            <span><i class="fas fa-images"></i> <span data-translate="slides_count">25 slides</span></span>
                        </div>
                    </div>
                    
                    <div class="system-card" data-system="respiratory">
                        <div class="system-icon">
                            <i class="fas fa-lungs"></i>
                        </div>
                        <h3 data-translate="system_respiratory">Respiratory System</h3>
                        <p data-translate="system_respiratory_desc">Lungs, airways, and gas exchange mechanisms</p>
                        <div class="system-stats">
                            <span><i class="fas fa-clock"></i> <span data-translate="duration_40min">40 min</span></span>
                            <span><i class="fas fa-images"></i> <span data-translate="slides_count_20">20 slides</span></span>
                        </div>
                    </div>
                    
                    <div class="system-card" data-system="nervous">
                        <div class="system-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                        <h3 data-translate="system_nervous">Nervous System</h3>
                        <p data-translate="system_nervous_desc">Brain, spinal cord, and neural networks</p>
                        <div class="system-stats">
                            <span><i class="fas fa-clock"></i> <span data-translate="duration_60min">60 min</span></span>
                            <span><i class="fas fa-images"></i> <span data-translate="slides_count_35">35 slides</span></span>
                        </div>
                    </div>
                    
                    <div class="system-card" data-system="musculoskeletal">
                        <div class="system-icon">
                            <i class="fas fa-dumbbell"></i>
                        </div>
                        <h3 data-translate="system_musculoskeletal">Musculoskeletal System</h3>
                        <p data-translate="system_musculoskeletal_desc">Bones, muscles, and movement mechanics</p>
                        <div class="system-stats">
                            <span><i class="fas fa-clock"></i> <span data-translate="duration_50min">50 min</span></span>
                            <span><i class="fas fa-images"></i> <span data-translate="slides_count_30">30 slides</span></span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Lecture Presentation Container -->
        <section class="lecture-presentation" id="lecturePresentation" style="display: none;">
            <div class="presentation-container">
                <!-- Presentation Header -->
                <div class="presentation-header">
                    <div class="presentation-title">
                        <h2 id="currentSystemTitle">Cardiovascular System</h2>
                        <span class="slide-counter">
                            <span id="currentSlide">1</span> / <span id="totalSlides">25</span>
                        </span>
                    </div>
                    <div class="presentation-controls">
                        <button class="control-btn" id="prevSlideBtn" title="Previous Slide">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="control-btn" id="playPauseBtn" title="Play/Pause">
                            <i class="fas fa-play"></i>
                        </button>
                        <button class="control-btn" id="nextSlideBtn" title="Next Slide">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                        <button class="control-btn" id="fullscreenBtn" title="Fullscreen">
                            <i class="fas fa-expand"></i>
                        </button>
                        <button class="control-btn" id="exitLectureBtn" title="Exit Lecture">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>

                <!-- Slide Content -->
                <div class="slide-container">
                    <div class="slide-content" id="slideContent">
                        <!-- Slide content will be dynamically loaded here -->
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-info">
                        <span id="progressText">0% Complete</span>
                        <span id="timeRemaining">45:00 remaining</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- 3D Anatomy Viewer -->
        <section class="anatomy-viewer" id="anatomyViewer" style="display: none;">
            <div class="viewer-container">
                <div class="viewer-header">
                    <h2 data-translate="anatomy_viewer_title">Interactive 3D Anatomy Viewer</h2>
                    <button class="control-btn" id="exitViewerBtn" title="Exit Viewer">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="viewer-content">
                    <div class="viewer-sidebar">
                        <h3 data-translate="select_system">Select Body System</h3>
                        <div class="system-selector">
                            <button class="system-btn active" data-system="cardiovascular">
                                <i class="fas fa-heart"></i>
                                <span data-translate="system_cardiovascular">Cardiovascular</span>
                            </button>
                            <button class="system-btn" data-system="respiratory">
                                <i class="fas fa-lungs"></i>
                                <span data-translate="system_respiratory">Respiratory</span>
                            </button>
                            <button class="system-btn" data-system="nervous">
                                <i class="fas fa-brain"></i>
                                <span data-translate="system_nervous">Nervous</span>
                            </button>
                            <button class="system-btn" data-system="musculoskeletal">
                                <i class="fas fa-dumbbell"></i>
                                <span data-translate="system_musculoskeletal">Musculoskeletal</span>
                            </button>
                        </div>
                        
                        <div class="viewer-controls">
                            <h4 data-translate="viewer_controls">Controls</h4>
                            <div class="control-group">
                                <label data-translate="opacity">Opacity</label>
                                <input type="range" id="opacitySlider" min="0" max="100" value="100">
                            </div>
                            <div class="control-group">
                                <label data-translate="rotation_speed">Rotation Speed</label>
                                <input type="range" id="rotationSlider" min="0" max="10" value="2">
                            </div>
                            <div class="control-buttons">
                                <button class="btn-reset" id="resetViewBtn" data-translate="reset_view">Reset View</button>
                                <button class="btn-info" id="showInfoBtn" data-translate="show_info">Show Info</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="viewer-main">
                        <div class="anatomy-display" id="anatomyDisplay">
                            <div class="anatomy-model">
                                <div class="model-placeholder">
                                    <i class="fas fa-heart pulse-animation"></i>
                                    <p data-translate="model_loading">Loading 3D Model...</p>
                                </div>
                            </div>
                            <div class="anatomy-info" id="anatomyInfo">
                                <h4 id="organTitle">Heart</h4>
                                <p id="organDescription">The heart is a muscular organ that pumps blood throughout the body...</p>
                                <div class="info-stats">
                                    <div class="stat">
                                        <span class="stat-label" data-translate="weight">Weight:</span>
                                        <span class="stat-value">300-400g</span>
                                    </div>
                                    <div class="stat">
                                        <span class="stat-label" data-translate="size">Size:</span>
                                        <span class="stat-value">12cm x 8cm</span>
                                    </div>
                                    <div class="stat">
                                        <span class="stat-label" data-translate="function">Function:</span>
                                        <span class="stat-value" data-translate="pumps_blood">Pumps Blood</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Author Information -->
        <section class="author-section">
            <div class="container">
                <div class="author-card">
                    <div class="author-image">
                        <img src="assets/images/author/dr-mohammed.jpg" alt="Dr. Mohammed Yagoub Esmail">
                    </div>
                    <div class="author-info">
                        <h3 data-translate="author_name">Dr. Mohammed Yagoub Esmail</h3>
                        <p class="author-title" data-translate="author_title">Professor of Biomedical Engineering</p>
                        <p class="author-affiliation" data-translate="author_affiliation">Sudan University of Science and Technology (SUST) - BME Department</p>
                        <div class="author-contact">
                            <p><i class="fas fa-envelope"></i> <EMAIL></p>
                            <p><i class="fas fa-phone"></i> +249912867327 | +966538076790</p>
                        </div>
                        <p class="copyright" data-translate="copyright">© 2025 Dr. Mohammed Yagoub Esmail. All rights reserved.</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer Section -->
    <footer class="main-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4 data-translate="footer_platform">Platform</h4>
                    <ul>
                        <li><a href="courses.html" data-translate="nav_courses">Courses</a></li>
                        <li><a href="lectures.html" data-translate="nav_lectures">Interactive Lectures</a></li>
                        <li><a href="dashboard.html" data-translate="nav_dashboard">Dashboard</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 data-translate="footer_support">Support</h4>
                    <ul>
                        <li><a href="help.html" data-translate="footer_help">Help Center</a></li>
                        <li><a href="contact.html" data-translate="nav_contact">Contact Us</a></li>
                        <li><a href="faq.html" data-translate="footer_faq">FAQ</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 data-translate="footer_legal">Legal</h4>
                    <ul>
                        <li><a href="privacy.html" data-translate="footer_privacy">Privacy Policy</a></li>
                        <li><a href="terms.html" data-translate="footer_terms">Terms of Service</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 data-translate="footer_connect">Connect</h4>
                    <div class="social-links">
                        <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p data-translate="footer_copyright">© 2025 BioEngage A&P LMS. Developed by Dr. Mohammed Yagoub Esmail, SUST-BME. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p data-translate="loading_text">Loading Interactive Lectures...</p>
        </div>
    </div>
</body>
</html>
