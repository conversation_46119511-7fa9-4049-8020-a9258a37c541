/* ===== LECTURES PAGE STYLES ===== */

/* Page-specific adjustments */
.lectures-page {
    background: var(--bg-primary);
}

.lectures-main {
    margin-top: 80px;
    min-height: calc(100vh - 80px);
}

/* ===== LECTURE HEADER ===== */
.lecture-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: var(--white);
    padding: var(--spacing-xxl) 0;
    position: relative;
    overflow: hidden;
}

.lecture-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('../images/anatomy-pattern.png') repeat;
    opacity: 0.1;
    animation: float 20s infinite linear;
}

.lecture-header-content {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: var(--spacing-xl);
    align-items: center;
    position: relative;
    z-index: 2;
}

.lecture-info h1 {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    animation: fadeInUp 1s ease-out;
}

.lecture-info p {
    font-size: var(--font-size-lg);
    opacity: 0.9;
    margin-bottom: var(--spacing-lg);
    animation: fadeInUp 1s ease-out 0.2s both;
}

.lecture-meta {
    display: flex;
    gap: var(--spacing-xl);
    flex-wrap: wrap;
    animation: fadeInUp 1s ease-out 0.4s both;
}

.lecture-meta span {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    opacity: 0.8;
}

.lecture-controls {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    animation: fadeInRight 1s ease-out 0.6s both;
}

.btn-start-lecture,
.btn-anatomy-viewer {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-lg);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    text-decoration: none;
    justify-content: center;
    min-width: 200px;
}

.btn-start-lecture {
    background: var(--secondary-color);
    color: var(--white);
}

.btn-start-lecture:hover {
    background: #e68900;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-anatomy-viewer {
    background: transparent;
    color: var(--white);
    border: 2px solid var(--white);
}

.btn-anatomy-viewer:hover {
    background: var(--white);
    color: var(--primary-color);
    transform: translateY(-2px);
}

/* ===== LECTURE SYSTEMS ===== */
.lecture-systems {
    padding: var(--spacing-xxl) 0;
    background: var(--white);
}

.lecture-systems h2 {
    text-align: center;
    font-size: var(--font-size-3xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-xxl);
}

.systems-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
}

.system-card {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    text-align: center;
    transition: var(--transition-normal);
    cursor: pointer;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.system-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-slow);
}

.system-card:hover::before {
    left: 100%;
}

.system-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
    border-color: var(--secondary-color);
}

.system-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg);
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-normal);
}

.system-card:hover .system-icon {
    transform: scale(1.1) rotate(5deg);
}

.system-icon i {
    font-size: var(--font-size-2xl);
    color: var(--white);
}

.system-card h3 {
    font-size: var(--font-size-xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    font-weight: 600;
}

.system-card p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.system-stats {
    display: flex;
    justify-content: space-between;
    gap: var(--spacing-md);
}

.system-stats span {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-light);
}

/* ===== LECTURE PRESENTATION ===== */
.lecture-presentation {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--black);
    z-index: 9999;
    display: flex;
    flex-direction: column;
}

.presentation-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.presentation-header {
    background: rgba(0, 0, 0, 0.9);
    padding: var(--spacing-md) var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--white);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.presentation-title h2 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-xs);
}

.slide-counter {
    font-size: var(--font-size-sm);
    opacity: 0.7;
}

.presentation-controls {
    display: flex;
    gap: var(--spacing-sm);
}

.control-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
}

.control-btn:hover {
    background: var(--secondary-color);
    transform: scale(1.1);
}

.control-btn:active {
    transform: scale(0.95);
}

.slide-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-lg);
    position: relative;
}

.slide-content {
    width: 100%;
    max-width: 1200px;
    height: 100%;
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xxl);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    box-shadow: var(--shadow-xl);
    position: relative;
    overflow: hidden;
}

.progress-container {
    background: rgba(0, 0, 0, 0.9);
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--white);
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-full);
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.progress-fill {
    height: 100%;
    background: var(--secondary-color);
    border-radius: var(--radius-full);
    transition: width 0.3s ease;
    width: 0%;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    font-size: var(--font-size-sm);
    opacity: 0.8;
}

/* ===== 3D ANATOMY VIEWER ===== */
.anatomy-viewer {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--dark-gray);
    z-index: 9999;
    color: var(--white);
}

.viewer-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.viewer-header {
    background: rgba(0, 0, 0, 0.9);
    padding: var(--spacing-md) var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.viewer-header h2 {
    font-size: var(--font-size-xl);
    color: var(--white);
}

.viewer-content {
    flex: 1;
    display: grid;
    grid-template-columns: 300px 1fr;
    height: calc(100vh - 80px);
}

.viewer-sidebar {
    background: rgba(0, 0, 0, 0.8);
    padding: var(--spacing-lg);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    overflow-y: auto;
}

.viewer-sidebar h3,
.viewer-sidebar h4 {
    color: var(--secondary-color);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-lg);
}

.system-selector {
    margin-bottom: var(--spacing-xl);
}

.system-btn {
    width: 100%;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--white);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-normal);
    margin-bottom: var(--spacing-sm);
}

.system-btn:hover,
.system-btn.active {
    background: var(--primary-color);
    border-color: var(--secondary-color);
}

.system-btn i {
    font-size: var(--font-size-lg);
    color: var(--secondary-color);
}

.control-group {
    margin-bottom: var(--spacing-lg);
}

.control-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-light);
}

.control-group input[type="range"] {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-full);
    outline: none;
    -webkit-appearance: none;
}

.control-group input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    background: var(--secondary-color);
    border-radius: 50%;
    cursor: pointer;
}

.control-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.btn-reset,
.btn-info {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--secondary-color);
    background: transparent;
    color: var(--secondary-color);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-normal);
    font-size: var(--font-size-sm);
}

.btn-reset:hover,
.btn-info:hover {
    background: var(--secondary-color);
    color: var(--white);
}

.viewer-main {
    background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
    position: relative;
    overflow: hidden;
}

.anatomy-display {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.anatomy-model {
    width: 400px;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 50%;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 153, 0, 0.3);
    animation: rotate 20s infinite linear;
}

.model-placeholder {
    text-align: center;
    color: var(--white);
}

.model-placeholder i {
    font-size: 120px;
    color: var(--secondary-color);
    margin-bottom: var(--spacing-md);
}

.model-placeholder p {
    font-size: var(--font-size-lg);
    opacity: 0.7;
}

.anatomy-info {
    position: absolute;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    background: rgba(0, 0, 0, 0.8);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    max-width: 300px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.anatomy-info h4 {
    color: var(--secondary-color);
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-md);
}

.anatomy-info p {
    color: var(--white);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
    opacity: 0.9;
}

.info-stats {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stat:last-child {
    border-bottom: none;
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-light);
}

.stat-value {
    font-size: var(--font-size-sm);
    color: var(--white);
    font-weight: 500;
}

/* ===== SLIDE CONTENT STYLES ===== */
.slide-content h2 {
    color: var(--primary-color);
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-xl);
    text-align: center;
}

.slide-image {
    text-align: center;
    margin: var(--spacing-xl) 0;
}

.slide-image img {
    max-width: 100%;
    height: 300px;
    object-fit: cover;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
}

.key-points {
    background: var(--bg-primary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    margin-top: var(--spacing-xl);
}

.key-points h3 {
    color: var(--secondary-color);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-xl);
}

.key-points ul {
    list-style: none;
    padding: 0;
}

.key-points li {
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-light);
    position: relative;
    padding-left: var(--spacing-lg);
}

.key-points li:before {
    content: '▶';
    color: var(--secondary-color);
    position: absolute;
    left: 0;
    top: var(--spacing-sm);
}

.key-points li:last-child {
    border-bottom: none;
}

.anatomy-details {
    margin-top: var(--spacing-xl);
}

.anatomy-details h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
    text-align: center;
    font-size: var(--font-size-xl);
}

.chambers-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.chamber {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    text-align: center;
    transition: var(--transition-normal);
}

.chamber:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.chamber h4 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
    color: var(--secondary-color);
}

.chamber p {
    font-size: var(--font-size-sm);
    opacity: 0.9;
}

.applications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.application {
    background: var(--white);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    transition: var(--transition-normal);
}

.application:hover {
    border-color: var(--secondary-color);
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.application h3 {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-md);
}

.application p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

.tech-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.tech-details span {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--bg-primary);
    border-radius: var(--radius-sm);
}

.system-components {
    background: var(--bg-primary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    margin-top: var(--spacing-xl);
}

.system-components h3 {
    color: var(--secondary-color);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-xl);
}

.system-components ul {
    list-style: none;
    padding: 0;
}

.system-components li {
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-light);
    position: relative;
    padding-left: var(--spacing-lg);
}

.system-components li:before {
    content: '●';
    color: var(--secondary-color);
    position: absolute;
    left: 0;
    top: var(--spacing-sm);
}

.system-components li:last-child {
    border-bottom: none;
}

.completion-message {
    text-align: center;
    padding: var(--spacing-xxl);
}

.completion-icon {
    font-size: 120px;
    color: var(--success-color);
    margin-bottom: var(--spacing-xl);
    animation: bounce 2s infinite;
}

.completion-message h2 {
    color: var(--primary-color);
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-lg);
}

.completion-message p {
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xxl);
    line-height: 1.6;
}

.completion-actions {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: center;
    flex-wrap: wrap;
}

.btn-primary,
.btn-secondary {
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-lg);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--accent-color);
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-30px);
    }
    60% {
        transform: translateY(-15px);
    }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .lecture-header-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-lg);
    }
    
    .lecture-info h1 {
        font-size: var(--font-size-3xl);
    }
    
    .lecture-meta {
        justify-content: center;
        gap: var(--spacing-md);
    }
    
    .lecture-controls {
        flex-direction: row;
        justify-content: center;
        gap: var(--spacing-md);
    }
    
    .btn-start-lecture,
    .btn-anatomy-viewer {
        min-width: 150px;
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--font-size-base);
    }
    
    .systems-grid {
        grid-template-columns: 1fr;
    }
    
    .viewer-content {
        grid-template-columns: 1fr;
    }
    
    .viewer-sidebar {
        display: none;
    }
    
    .anatomy-info {
        position: relative;
        top: auto;
        right: auto;
        margin-top: var(--spacing-lg);
        max-width: none;
    }
    
    .anatomy-model {
        width: 300px;
        height: 300px;
    }
    
    .model-placeholder i {
        font-size: 80px;
    }
}

@media (max-width: 480px) {
    .lecture-controls {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-start-lecture,
    .btn-anatomy-viewer {
        width: 100%;
        max-width: 250px;
    }
    
    .presentation-header {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }
    
    .slide-content {
        padding: var(--spacing-lg);
    }
    
    .anatomy-model {
        width: 250px;
        height: 250px;
    }
}
