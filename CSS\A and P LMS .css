/* General Styles */
body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  background-color: #f4f4f9;
  color: #333;
}

.container {
  width: 80%;
  margin: 0 auto;
}

header {
  background-color: #0f4c75;
  padding: 20px 0;
}

nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

nav a {
  color: white;
  text-decoration: none;
  padding: 10px;
}

nav .cta {
  background-color: #ff9900;
  border-radius: 5px;
  padding: 10px 20px;
}

#hero {
  background-color: #0f4c75;
  color: white;
  padding: 100px 0;
  text-align: center;
}

#hero h1 {
  font-size: 2.5rem;
}

#hero p {
  font-size: 1.2rem;
}

.cta-buttons a {
  text-decoration: none;
  padding: 15px 30px;
  border-radius: 5px;
  margin: 10px;
}

.btn-primary {
  background-color: #ff9900;
  color: white;
}

.btn-secondary {
  background-color: transparent;
  border: 2px solid #ff9900;
  color: #ff9900;
}

#features {
  background-color: white;
  padding: 50px 0;
}

#features h2 {
  text-align: center;
  font-size: 2rem;
  margin-bottom: 40px;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.feature-item {
  background-color: #f4f4f9;
  padding: 20px;
  text-align: center;
  border-radius: 8px;
}

.feature-item h3 {
  font-size: 1.5rem;
  margin-top: 10px;
}

#courses {
  padding: 50px 0;
}

#courses h2 {
  text-align: center;
  font-size: 2rem;
  margin-bottom: 40px;
}

.course-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.course-card {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.course-card h3 {
  font-size: 1.5rem;
  margin-bottom: 10px;
}

.course-card a {
  display: inline-block;
  background-color: #ff9900;
  color: white;
  padding: 10px 20px;
  border-radius: 5px;
  text-decoration: none;
}

#cta {
  background-color: #0f4c75;
  color: white;
  padding: 50px 0;
  text-align: center;
}

footer {
  background-color: #333;
  color: white;
  padding: 20px 0;
  text-align: center;
}

footer a {
  color: white;
  text-decoration: none;
}

footer a:hover {
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
  .feature-grid, .course-grid {
    grid-template-columns: 1fr 1fr;
  }

  .cta-buttons a {
    padding: 10px 20px;
  }
}
