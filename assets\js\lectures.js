// ===== INTERACTIVE LECTURES SYSTEM =====

// Global variables
let currentSlide = 0;
let totalSlides = 0;
let currentSystem = 'cardiovascular';
let isPlaying = false;
let slideInterval = null;
let slideData = {};

// DOM Elements
let lecturePresentation = null;
let anatomyViewer = null;
let slideContent = null;
let progressFill = null;
let currentSlideSpan = null;
let totalSlidesSpan = null;
let progressText = null;
let timeRemaining = null;

// Initialize lectures system
document.addEventListener('DOMContentLoaded', function() {
    initializeLectures();
});

function initializeLectures() {
    // Get DOM elements
    lecturePresentation = document.getElementById('lecturePresentation');
    anatomyViewer = document.getElementById('anatomyViewer');
    slideContent = document.getElementById('slideContent');
    progressFill = document.getElementById('progressFill');
    currentSlideSpan = document.getElementById('currentSlide');
    totalSlidesSpan = document.getElementById('totalSlides');
    progressText = document.getElementById('progressText');
    timeRemaining = document.getElementById('timeRemaining');

    // Initialize slide data
    initializeSlideData();
    
    // Add event listeners
    addEventListeners();
    
    console.log('Interactive Lectures System initialized');
}

function initializeSlideData() {
    slideData = {
        cardiovascular: {
            title: "Cardiovascular System",
            titleAr: "الجهاز القلبي الوعائي",
            duration: 45,
            slides: [
                {
                    title: "Introduction to Cardiovascular System",
                    titleAr: "مقدمة في الجهاز القلبي الوعائي",
                    content: `
                        <h2>Cardiovascular System Overview</h2>
                        <div class="slide-image">
                            <img src="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=600&h=400&fit=crop" alt="Heart anatomy">
                        </div>
                        <p>The cardiovascular system consists of the heart, blood vessels, and blood. It's responsible for transporting nutrients, oxygen, and waste products throughout the body.</p>
                        <div class="key-points">
                            <h3>Key Components:</h3>
                            <ul>
                                <li>Heart - The muscular pump</li>
                                <li>Arteries - Carry blood away from heart</li>
                                <li>Veins - Return blood to heart</li>
                                <li>Capillaries - Site of gas/nutrient exchange</li>
                            </ul>
                        </div>
                    `,
                    contentAr: `
                        <h2>نظرة عامة على الجهاز القلبي الوعائي</h2>
                        <div class="slide-image">
                            <img src="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=600&h=400&fit=crop" alt="تشريح القلب">
                        </div>
                        <p>يتكون الجهاز القلبي الوعائي من القلب والأوعية الدموية والدم. وهو مسؤول عن نقل المواد الغذائية والأكسجين والفضلات في جميع أنحاء الجسم.</p>
                        <div class="key-points">
                            <h3>المكونات الرئيسية:</h3>
                            <ul>
                                <li>القلب - المضخة العضلية</li>
                                <li>الشرايين - تحمل الدم بعيداً عن القلب</li>
                                <li>الأوردة - تعيد الدم إلى القلب</li>
                                <li>الشعيرات الدموية - موقع تبادل الغازات والمواد الغذائية</li>
                            </ul>
                        </div>
                    `
                },
                {
                    title: "Heart Anatomy and Function",
                    titleAr: "تشريح ووظيفة القلب",
                    content: `
                        <h2>Heart Structure and Function</h2>
                        <div class="slide-image">
                            <img src="https://images.unsplash.com/photo-1628348068343-c6a848d2b6dd?w=600&h=400&fit=crop" alt="Heart chambers">
                        </div>
                        <p>The heart is a four-chambered muscular organ that pumps blood throughout the body through coordinated contractions.</p>
                        <div class="anatomy-details">
                            <h3>Heart Chambers:</h3>
                            <div class="chambers-grid">
                                <div class="chamber">
                                    <h4>Right Atrium</h4>
                                    <p>Receives deoxygenated blood from body</p>
                                </div>
                                <div class="chamber">
                                    <h4>Right Ventricle</h4>
                                    <p>Pumps blood to lungs</p>
                                </div>
                                <div class="chamber">
                                    <h4>Left Atrium</h4>
                                    <p>Receives oxygenated blood from lungs</p>
                                </div>
                                <div class="chamber">
                                    <h4>Left Ventricle</h4>
                                    <p>Pumps blood to body</p>
                                </div>
                            </div>
                        </div>
                    `,
                    contentAr: `
                        <h2>بنية ووظيفة القلب</h2>
                        <div class="slide-image">
                            <img src="https://images.unsplash.com/photo-1628348068343-c6a848d2b6dd?w=600&h=400&fit=crop" alt="حجرات القلب">
                        </div>
                        <p>القلب عضو عضلي ذو أربع حجرات يضخ الدم في جميع أنحاء الجسم من خلال انقباضات منسقة.</p>
                        <div class="anatomy-details">
                            <h3>حجرات القلب:</h3>
                            <div class="chambers-grid">
                                <div class="chamber">
                                    <h4>الأذين الأيمن</h4>
                                    <p>يستقبل الدم غير المؤكسج من الجسم</p>
                                </div>
                                <div class="chamber">
                                    <h4>البطين الأيمن</h4>
                                    <p>يضخ الدم إلى الرئتين</p>
                                </div>
                                <div class="chamber">
                                    <h4>الأذين الأيسر</h4>
                                    <p>يستقبل الدم المؤكسج من الرئتين</p>
                                </div>
                                <div class="chamber">
                                    <h4>البطين الأيسر</h4>
                                    <p>يضخ الدم إلى الجسم</p>
                                </div>
                            </div>
                        </div>
                    `
                },
                {
                    title: "Biomedical Engineering Applications",
                    titleAr: "تطبيقات الهندسة الطبية الحيوية",
                    content: `
                        <h2>BME Applications in Cardiovascular System</h2>
                        <div class="slide-image">
                            <img src="https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=600&h=400&fit=crop" alt="Medical devices">
                        </div>
                        <div class="applications-grid">
                            <div class="application">
                                <h3>Pacemakers</h3>
                                <p>Electronic devices that regulate heart rhythm</p>
                                <div class="tech-details">
                                    <span>Technology: Electrical stimulation</span>
                                    <span>Materials: Titanium, silicone</span>
                                </div>
                            </div>
                            <div class="application">
                                <h3>Artificial Heart Valves</h3>
                                <p>Mechanical or biological valve replacements</p>
                                <div class="tech-details">
                                    <span>Technology: Fluid dynamics</span>
                                    <span>Materials: Carbon, polymers</span>
                                </div>
                            </div>
                            <div class="application">
                                <h3>Ventricular Assist Devices</h3>
                                <p>Mechanical pumps for heart failure patients</p>
                                <div class="tech-details">
                                    <span>Technology: Centrifugal pumps</span>
                                    <span>Materials: Biocompatible alloys</span>
                                </div>
                            </div>
                        </div>
                    `,
                    contentAr: `
                        <h2>تطبيقات الهندسة الطبية الحيوية في الجهاز القلبي الوعائي</h2>
                        <div class="slide-image">
                            <img src="https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=600&h=400&fit=crop" alt="الأجهزة الطبية">
                        </div>
                        <div class="applications-grid">
                            <div class="application">
                                <h3>أجهزة تنظيم ضربات القلب</h3>
                                <p>أجهزة إلكترونية تنظم إيقاع القلب</p>
                                <div class="tech-details">
                                    <span>التكنولوجيا: التحفيز الكهربائي</span>
                                    <span>المواد: التيتانيوم، السيليكون</span>
                                </div>
                            </div>
                            <div class="application">
                                <h3>صمامات القلب الاصطناعية</h3>
                                <p>بدائل صمامات ميكانيكية أو بيولوجية</p>
                                <div class="tech-details">
                                    <span>التكنولوجيا: ديناميكا الموائع</span>
                                    <span>المواد: الكربون، البوليمرات</span>
                                </div>
                            </div>
                            <div class="application">
                                <h3>أجهزة مساعدة البطين</h3>
                                <p>مضخات ميكانيكية لمرضى فشل القلب</p>
                                <div class="tech-details">
                                    <span>التكنولوجيا: المضخات الطاردة المركزية</span>
                                    <span>المواد: سبائك متوافقة حيوياً</span>
                                </div>
                            </div>
                        </div>
                    `
                }
            ]
        },
        respiratory: {
            title: "Respiratory System",
            titleAr: "الجهاز التنفسي",
            duration: 40,
            slides: [
                {
                    title: "Respiratory System Overview",
                    titleAr: "نظرة عامة على الجهاز التنفسي",
                    content: `
                        <h2>Respiratory System Structure</h2>
                        <div class="slide-image">
                            <img src="https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=600&h=400&fit=crop" alt="Lungs anatomy">
                        </div>
                        <p>The respiratory system facilitates gas exchange between the atmosphere and blood, providing oxygen and removing carbon dioxide.</p>
                        <div class="system-components">
                            <h3>Main Components:</h3>
                            <ul>
                                <li>Upper Airways: Nose, pharynx, larynx</li>
                                <li>Lower Airways: Trachea, bronchi, bronchioles</li>
                                <li>Alveoli: Gas exchange units</li>
                                <li>Respiratory Muscles: Diaphragm, intercostals</li>
                            </ul>
                        </div>
                    `
                }
            ]
        },
        nervous: {
            title: "Nervous System",
            titleAr: "الجهاز العصبي",
            duration: 60,
            slides: [
                {
                    title: "Nervous System Overview",
                    titleAr: "نظرة عامة على الجهاز العصبي",
                    content: `
                        <h2>Central and Peripheral Nervous Systems</h2>
                        <div class="slide-image">
                            <img src="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=600&h=400&fit=crop" alt="Brain anatomy">
                        </div>
                        <p>The nervous system controls and coordinates body functions through electrical and chemical signals.</p>
                    `
                }
            ]
        },
        musculoskeletal: {
            title: "Musculoskeletal System",
            titleAr: "الجهاز العضلي الهيكلي",
            duration: 50,
            slides: [
                {
                    title: "Musculoskeletal System Overview",
                    titleAr: "نظرة عامة على الجهاز العضلي الهيكلي",
                    content: `
                        <h2>Bones, Muscles, and Movement</h2>
                        <div class="slide-image">
                            <img src="https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=600&h=400&fit=crop" alt="Skeleton anatomy">
                        </div>
                        <p>The musculoskeletal system provides structural support and enables movement through the coordinated action of bones, muscles, and joints.</p>
                    `
                }
            ]
        }
    };
}

function addEventListeners() {
    // Start lecture button
    document.getElementById('startLectureBtn')?.addEventListener('click', startLecture);
    
    // Anatomy viewer button
    document.getElementById('anatomyViewerBtn')?.addEventListener('click', openAnatomyViewer);
    
    // System cards
    document.querySelectorAll('.system-card').forEach(card => {
        card.addEventListener('click', () => {
            const system = card.dataset.system;
            if (system) {
                currentSystem = system;
                startLecture();
            }
        });
    });
    
    // Presentation controls
    document.getElementById('prevSlideBtn')?.addEventListener('click', previousSlide);
    document.getElementById('nextSlideBtn')?.addEventListener('click', nextSlide);
    document.getElementById('playPauseBtn')?.addEventListener('click', togglePlayPause);
    document.getElementById('fullscreenBtn')?.addEventListener('click', toggleFullscreen);
    document.getElementById('exitLectureBtn')?.addEventListener('click', exitLecture);
    
    // Anatomy viewer controls
    document.getElementById('exitViewerBtn')?.addEventListener('click', exitAnatomyViewer);
    document.querySelectorAll('.system-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            document.querySelectorAll('.system-btn').forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            updateAnatomyModel(btn.dataset.system);
        });
    });
    
    // Keyboard controls
    document.addEventListener('keydown', handleKeyboardControls);
}

function startLecture() {
    if (!slideData[currentSystem]) return;
    
    const systemData = slideData[currentSystem];
    totalSlides = systemData.slides.length;
    currentSlide = 0;
    
    // Update UI
    document.getElementById('currentSystemTitle').textContent = getCurrentLanguage() === 'ar' ? systemData.titleAr : systemData.title;
    totalSlidesSpan.textContent = totalSlides;
    
    // Show presentation
    lecturePresentation.style.display = 'block';
    document.body.style.overflow = 'hidden';
    
    // Load first slide
    loadSlide(0);
    
    // Start auto-play
    startAutoPlay();
}

function loadSlide(slideIndex) {
    if (!slideData[currentSystem] || slideIndex >= slideData[currentSystem].slides.length) return;
    
    const slide = slideData[currentSystem].slides[slideIndex];
    const isArabic = getCurrentLanguage() === 'ar';
    
    // Update slide content
    slideContent.innerHTML = isArabic ? slide.contentAr || slide.content : slide.content;
    
    // Update counters
    currentSlideSpan.textContent = slideIndex + 1;
    
    // Update progress
    const progress = ((slideIndex + 1) / totalSlides) * 100;
    progressFill.style.width = progress + '%';
    progressText.textContent = Math.round(progress) + '% Complete';
    
    // Update time remaining
    const systemData = slideData[currentSystem];
    const timePerSlide = (systemData.duration * 60) / totalSlides;
    const remainingSlides = totalSlides - slideIndex - 1;
    const remainingSeconds = Math.round(remainingSlides * timePerSlide);
    const minutes = Math.floor(remainingSeconds / 60);
    const seconds = remainingSeconds % 60;
    timeRemaining.textContent = `${minutes}:${seconds.toString().padStart(2, '0')} remaining`;
    
    // Add slide animation
    slideContent.style.opacity = '0';
    slideContent.style.transform = 'translateX(50px)';
    
    setTimeout(() => {
        slideContent.style.opacity = '1';
        slideContent.style.transform = 'translateX(0)';
        slideContent.style.transition = 'all 0.5s ease-out';
    }, 100);
}

function nextSlide() {
    if (currentSlide < totalSlides - 1) {
        currentSlide++;
        loadSlide(currentSlide);
    } else {
        // End of presentation
        stopAutoPlay();
        showCompletionMessage();
    }
}

function previousSlide() {
    if (currentSlide > 0) {
        currentSlide--;
        loadSlide(currentSlide);
    }
}

function togglePlayPause() {
    const playPauseBtn = document.getElementById('playPauseBtn');
    const icon = playPauseBtn.querySelector('i');
    
    if (isPlaying) {
        stopAutoPlay();
        icon.className = 'fas fa-play';
    } else {
        startAutoPlay();
        icon.className = 'fas fa-pause';
    }
}

function startAutoPlay() {
    if (slideInterval) clearInterval(slideInterval);
    
    isPlaying = true;
    const playPauseBtn = document.getElementById('playPauseBtn');
    const icon = playPauseBtn.querySelector('i');
    icon.className = 'fas fa-pause';
    
    // Auto-advance every 10 seconds
    slideInterval = setInterval(() => {
        nextSlide();
    }, 10000);
}

function stopAutoPlay() {
    if (slideInterval) {
        clearInterval(slideInterval);
        slideInterval = null;
    }
    isPlaying = false;
}

function toggleFullscreen() {
    if (!document.fullscreenElement) {
        lecturePresentation.requestFullscreen();
    } else {
        document.exitFullscreen();
    }
}

function exitLecture() {
    stopAutoPlay();
    lecturePresentation.style.display = 'none';
    document.body.style.overflow = 'auto';
}

function showCompletionMessage() {
    const completionHtml = `
        <div class="completion-message">
            <div class="completion-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h2>${getCurrentLanguage() === 'ar' ? 'تم إكمال المحاضرة!' : 'Lecture Completed!'}</h2>
            <p>${getCurrentLanguage() === 'ar' ? 'لقد أكملت بنجاح محاضرة ' + slideData[currentSystem].titleAr : 'You have successfully completed the ' + slideData[currentSystem].title + ' lecture'}</p>
            <div class="completion-actions">
                <button class="btn-primary" onclick="exitLecture()">${getCurrentLanguage() === 'ar' ? 'العودة إلى القائمة' : 'Back to Menu'}</button>
                <button class="btn-secondary" onclick="restartLecture()">${getCurrentLanguage() === 'ar' ? 'إعادة المحاضرة' : 'Restart Lecture'}</button>
            </div>
        </div>
    `;
    slideContent.innerHTML = completionHtml;
}

function restartLecture() {
    currentSlide = 0;
    loadSlide(0);
    startAutoPlay();
}

function openAnatomyViewer() {
    anatomyViewer.style.display = 'block';
    document.body.style.overflow = 'hidden';
    updateAnatomyModel('cardiovascular');
}

function exitAnatomyViewer() {
    anatomyViewer.style.display = 'none';
    document.body.style.overflow = 'auto';
}

function updateAnatomyModel(system) {
    const anatomyDisplay = document.getElementById('anatomyDisplay');
    const anatomyInfo = document.getElementById('anatomyInfo');
    
    // Update model (placeholder for now)
    const modelPlaceholder = anatomyDisplay.querySelector('.model-placeholder');
    const icons = {
        cardiovascular: 'fas fa-heart',
        respiratory: 'fas fa-lungs',
        nervous: 'fas fa-brain',
        musculoskeletal: 'fas fa-dumbbell'
    };
    
    if (modelPlaceholder) {
        const icon = modelPlaceholder.querySelector('i');
        icon.className = icons[system] || 'fas fa-heart';
        
        // Add system-specific animation
        icon.classList.add(system === 'cardiovascular' ? 'pulse-animation' : 'float-animation');
    }
    
    // Update info panel
    const systemInfo = {
        cardiovascular: {
            title: 'Heart',
            titleAr: 'القلب',
            description: 'The heart is a muscular organ that pumps blood throughout the body...',
            descriptionAr: 'القلب عضو عضلي يضخ الدم في جميع أنحاء الجسم...',
            weight: '300-400g',
            size: '12cm x 8cm',
            function: 'Pumps Blood',
            functionAr: 'ضخ الدم'
        },
        respiratory: {
            title: 'Lungs',
            titleAr: 'الرئتان',
            description: 'The lungs are the primary organs of respiration...',
            descriptionAr: 'الرئتان هما العضوان الأساسيان للتنفس...',
            weight: '1.3kg',
            size: '25cm x 15cm',
            function: 'Gas Exchange',
            functionAr: 'تبادل الغازات'
        }
    };
    
    const info = systemInfo[system] || systemInfo.cardiovascular;
    const isArabic = getCurrentLanguage() === 'ar';
    
    document.getElementById('organTitle').textContent = isArabic ? info.titleAr : info.title;
    document.getElementById('organDescription').textContent = isArabic ? info.descriptionAr : info.description;
    
    // Update stats
    const stats = anatomyInfo.querySelectorAll('.stat-value');
    if (stats.length >= 3) {
        stats[0].textContent = info.weight;
        stats[1].textContent = info.size;
        stats[2].textContent = isArabic ? info.functionAr : info.function;
    }
}

function handleKeyboardControls(e) {
    if (lecturePresentation.style.display === 'block') {
        switch(e.key) {
            case 'ArrowLeft':
                previousSlide();
                break;
            case 'ArrowRight':
                nextSlide();
                break;
            case ' ':
                e.preventDefault();
                togglePlayPause();
                break;
            case 'Escape':
                exitLecture();
                break;
            case 'f':
            case 'F':
                toggleFullscreen();
                break;
        }
    }
    
    if (anatomyViewer.style.display === 'block' && e.key === 'Escape') {
        exitAnatomyViewer();
    }
}

// Export functions for global use
window.startLecture = startLecture;
window.exitLecture = exitLecture;
window.restartLecture = restartLecture;
window.openAnatomyViewer = openAnatomyViewer;
window.exitAnatomyViewer = exitAnatomyViewer;
